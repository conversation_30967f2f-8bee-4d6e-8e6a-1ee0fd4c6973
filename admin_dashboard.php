<?php
session_start();
require_once 'config.php';

// Check if admin is logged in
if (!isset($_SESSION['admin_logged_in'])) {
    header('Location: admin_login.php');
    exit;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .stat-number { font-size: 2em; font-weight: bold; color: #2196F3; }
        .stat-label { color: #666; margin-top: 5px; }
        .table-container { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        table { width: 100%; border-collapse: collapse; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; font-weight: bold; }
        .online-indicator { color: #4CAF50; font-weight: bold; }
        .loading { color: #666; font-style: italic; }
        .refresh-btn { background: #2196F3; color: white; border: none; padding: 10px 20px; border-radius: 4px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Admin Dashboard</h1>
        <button class="refresh-btn" onclick="refreshData()">Refresh Data</button>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="unique-users">-</div>
                <div class="stat-label">Unique Users (24h)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="ip-users">-</div>
                <div class="stat-label">Different Users per IP</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="online-users">-</div>
                <div class="stat-label">Currently Online</div>
            </div>
        </div>

        <div class="table-container">
            <h3>Online Users (Last 5 minutes)</h3>
            <table>
                <thead>
                    <tr>
                        <th>User ID</th>
                        <th>IP Address</th>
                        <th>Last Activity</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody id="online-users-table">
                    <tr><td colspan="4" class="loading">Loading...</td></tr>
                </tbody>
            </table>
        </div>

        <div class="table-container" style="margin-top: 20px;">
            <h3>Recent 20 Logins</h3>
            <table>
                <thead>
                    <tr>
                        <th>User ID</th>
                        <th>IP Address</th>
                        <th>User Agent</th>
                        <th>Login Time</th>
                    </tr>
                </thead>
                <tbody id="recent-logins-table">
                    <tr><td colspan="4" class="loading">Loading...</td></tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function refreshData() {
            fetchStats();
            fetchOnlineUsers();
            fetchRecentLogins();
        }

        function fetchStats() {
            fetch('api/dashboard_stats.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('unique-users').textContent = data.unique_users_24h;
                    document.getElementById('ip-users').textContent = data.max_users_per_ip;
                    document.getElementById('online-users').textContent = data.online_users;
                })
                .catch(error => console.error('Error fetching stats:', error));
        }

        function fetchOnlineUsers() {
            fetch('api/online_users.php')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('online-users-table');
                    if (data.length === 0) {
                        tbody.innerHTML = '<tr><td colspan="4">No online users</td></tr>';
                        return;
                    }
                    
                    tbody.innerHTML = data.map(user => `
                        <tr>
                            <td>${user.user_id}</td>
                            <td>${user.ip_address}</td>
                            <td>${user.last_activity}</td>
                            <td><span class="online-indicator">● Online</span></td>
                        </tr>
                    `).join('');
                })
                .catch(error => console.error('Error fetching online users:', error));
        }

        function fetchRecentLogins() {
            fetch('api/recent_logins.php')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.getElementById('recent-logins-table');
                    tbody.innerHTML = data.map(login => `
                        <tr>
                            <td>${login.user_id}</td>
                            <td>${login.ip_address}</td>
                            <td>${login.user_agent.substring(0, 50)}...</td>
                            <td>${login.login_time}</td>
                        </tr>
                    `).join('');
                })
                .catch(error => console.error('Error fetching recent logins:', error));
        }

        // Auto refresh every 30 seconds
        setInterval(refreshData, 30000);
        
        // Initial load
        refreshData();
    </script>
</body>
</html>