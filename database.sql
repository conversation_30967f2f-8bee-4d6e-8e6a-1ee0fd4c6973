-- Create database and table for user logins
CREATE DATABASE IF NOT EXISTS admin_dashboard;
USE admin_dashboard;

-- Create user_logins table
CREATE TABLE IF NOT EXISTS user_logins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_ip_address (ip_address),
    INDEX idx_login_time (login_time)
);

-- Insert some sample data for testing
INSERT INTO user_logins (user_id, ip_address, user_agent, login_time) VALUES
('user1', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 1 HOUR),
('user2', '*************', 'Mozilla/5.0 (<PERSON>; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 2 HOUR),
('user3', '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', NOW() - INTERVAL 3 MINUTE),
('user1', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 10 MINUTE),
('user4', '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', NOW() - INTERVAL 1 MINUTE),
('user5', '*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0', NOW() - INTERVAL 5 HOUR),
('user2', '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', NOW() - INTERVAL 30 MINUTE),
('user6', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', NOW() - INTERVAL 2 MINUTE);
