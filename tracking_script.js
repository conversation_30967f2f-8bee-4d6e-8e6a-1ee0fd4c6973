/**
 * CheckMe.One Website Tracking Script
 * Add this script to checkme.one website to track user hits
 */

(function() {
    'use strict';
    
    // Configuration
    const TRACKING_API_URL = 'https://your-monitoring-domain.com/monitor_api.php';
    const WEBSITE_ID = 1; // CheckMe.One website ID
    
    // Generate or get session ID
    function getSessionId() {
        let sessionId = sessionStorage.getItem('checkme_session_id');
        if (!sessionId) {
            sessionId = 'sess_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
            sessionStorage.setItem('checkme_session_id', sessionId);
        }
        return sessionId;
    }
    
    // Get visitor's location (approximate)
    function getVisitorLocation() {
        return new Promise((resolve) => {
            // Try to get location from IP geolocation service
            fetch('https://ipapi.co/json/')
                .then(response => response.json())
                .then(data => {
                    resolve({
                        country: data.country_name || 'Unknown',
                        city: data.city || 'Unknown'
                    });
                })
                .catch(() => {
                    // Fallback to timezone-based detection
                    const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
                    let country = 'Unknown';
                    let city = 'Unknown';
                    
                    if (timezone.includes('Dhaka')) {
                        country = 'Bangladesh';
                        city = 'Dhaka';
                    } else if (timezone.includes('Kolkata')) {
                        country = 'India';
                        city = 'Kolkata';
                    } else if (timezone.includes('Karachi')) {
                        country = 'Pakistan';
                        city = 'Karachi';
                    }
                    
                    resolve({ country, city });
                });
        });
    }
    
    // Track page hit
    async function trackPageHit() {
        try {
            const location = await getVisitorLocation();
            
            const hitData = {
                website_id: WEBSITE_ID,
                page_url: window.location.href,
                referer: document.referrer || '',
                session_id: getSessionId(),
                country: location.country,
                city: location.city,
                user_agent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
            
            // Send tracking data
            const formData = new FormData();
            Object.keys(hitData).forEach(key => {
                formData.append(key, hitData[key]);
            });
            
            fetch(TRACKING_API_URL + '?action=add_hit', {
                method: 'POST',
                body: formData,
                mode: 'cors'
            }).catch(error => {
                console.log('Tracking error:', error);
            });
            
        } catch (error) {
            console.log('Tracking initialization error:', error);
        }
    }
    
    // Track page visibility changes
    function trackVisibilityChange() {
        if (!document.hidden) {
            trackPageHit();
        }
    }
    
    // Initialize tracking
    function initTracking() {
        // Track initial page load
        trackPageHit();
        
        // Track when user returns to tab
        document.addEventListener('visibilitychange', trackVisibilityChange);
        
        // Track hash changes (SPA navigation)
        window.addEventListener('hashchange', trackPageHit);
        
        // Track popstate (browser back/forward)
        window.addEventListener('popstate', trackPageHit);
    }
    
    // Start tracking when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initTracking);
    } else {
        initTracking();
    }
    
})();

/**
 * Usage Instructions:
 * 
 * 1. Update TRACKING_API_URL to your monitoring server URL
 * 2. Add this script to checkme.one website:
 * 
 * <script src="https://your-monitoring-domain.com/tracking_script.js"></script>
 * 
 * Or inline:
 * <script>
 *   // Paste the entire script content here
 * </script>
 * 
 * 3. The script will automatically:
 *    - Track page views
 *    - Detect visitor location
 *    - Generate unique session IDs
 *    - Send data to your monitoring dashboard
 * 
 * 4. Make sure your monitoring server allows CORS requests from checkme.one
 */
