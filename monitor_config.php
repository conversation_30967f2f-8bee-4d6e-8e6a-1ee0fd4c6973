<?php
// Website monitoring configuration
define('DB_HOST', 'localhost');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_NAME', 'website_monitor');

// Create database connection
function getDBConnection() {
    try {
        $pdo = new PDO(
            "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
            DB_USER,
            DB_PASS,
            [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false
            ]
        );
        return $pdo;
    } catch (PDOException $e) {
        die("Database connection failed: " . $e->getMessage());
    }
}

// Websites to monitor
$websites = [
    [
        'id' => 1,
        'name' => 'CheckMe.One',
        'url' => 'https://checkme.one',
        'expected_status' => 200,
        'timeout' => 30,
        'check_interval' => 60 // seconds
    ]
    // Add more websites here if needed
];

// Set timezone
date_default_timezone_set('Asia/Dhaka');

// Function to check website status
function checkWebsiteStatus($url, $timeout = 30) {
    $start_time = microtime(true);
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, $timeout);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Website Monitor Bot 1.0');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_NOBODY, true); // HEAD request only
    curl_setopt($ch, CURLOPT_HEADER, true);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $total_time = curl_getinfo($ch, CURLINFO_TOTAL_TIME);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    $end_time = microtime(true);
    $response_time = round(($end_time - $start_time) * 1000, 2); // milliseconds
    
    return [
        'status_code' => $http_code,
        'response_time' => $response_time,
        'is_online' => ($http_code >= 200 && $http_code < 400),
        'error' => $error,
        'total_time' => round($total_time * 1000, 2)
    ];
}

// Function to log monitoring result
function logMonitoringResult($pdo, $website_id, $result) {
    try {
        $stmt = $pdo->prepare("
            INSERT INTO monitoring_logs 
            (website_id, status_code, response_time, is_online, error_message, check_time) 
            VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $website_id,
            $result['status_code'],
            $result['response_time'],
            $result['is_online'] ? 1 : 0,
            $result['error']
        ]);
        
        return true;
    } catch (Exception $e) {
        error_log("Failed to log monitoring result: " . $e->getMessage());
        return false;
    }
}
?>
