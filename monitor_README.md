# Website Monitor for CheckMe.One

এই প্রজেক্টটি checkme.one website এর জন্য একটি complete monitoring solution যা real-time status tracking, uptime monitoring, এবং response time analysis প্রদান করে।

## 🎯 Features

- **Real-time Status Monitoring** - checkme.one এর current status (Online/Offline)
- **Response Time Tracking** - Website এর response time monitoring
- **Uptime Statistics** - 24h, 7d, 30d uptime percentage
- **Historical Data** - Response time charts এবং logs
- **Automated Monitoring** - Cron job দিয়ে automatic checking
- **Manual Check** - Instant manual website check
- **Beautiful Dashboard** - Modern responsive UI

## 📁 Files Structure

```
├── monitor_database.sql    # Database schema
├── monitor_config.php      # Configuration এবং core functions
├── monitor_api.php         # AJAX API endpoints
├── monitor.php             # Main dashboard page
├── monitor_script.js       # JavaScript for real-time updates
├── monitor_cron.php        # Automated monitoring script
└── monitor_README.md       # This documentation
```

## 🚀 Installation

### 1. Database Setup
```sql
-- MySQL/phpMyAdmin এ monitor_database.sql import করুন
mysql -u root -p < monitor_database.sql
```

### 2. Configuration
`monitor_config.php` এ database credentials update করুন:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'website_monitor');
```

### 3. Web Server Setup
Files গুলো web server এ upload করুন অথবা local server run করুন:
```bash
php -S localhost:8000
```

### 4. Automated Monitoring (Optional)
Cron job setup করুন automatic monitoring এর জন্য:
```bash
# Every minute check
* * * * * /usr/bin/php /path/to/monitor_cron.php

# Every 5 minutes check
*/5 * * * * /usr/bin/php /path/to/monitor_cron.php
```

## 📊 Dashboard Features

### Current Status Card
- **Website Status**: Online/Offline indicator
- **HTTP Status Code**: Response code (200, 404, 500, etc.)
- **Response Time**: Current response time in milliseconds
- **Last Check Time**: When was the last check performed

### Metrics Cards
- **24h Uptime**: Uptime percentage for last 24 hours
- **7d Uptime**: Uptime percentage for last 7 days
- **Avg Response**: Average response time for last 24 hours
- **Total Checks**: Number of checks performed in last 24 hours

### Response Time Chart
- Real-time line chart showing response time trends
- Last 24 hours data visualization
- Color-coded based on performance

### Recent Checks Log
- Last 15 monitoring results
- Success/failure indicators
- Response times and error messages

## 🔧 API Endpoints

- `monitor_api.php?action=current_status` - Current website status
- `monitor_api.php?action=uptime_stats` - Uptime statistics
- `monitor_api.php?action=response_time_chart` - Chart data
- `monitor_api.php?action=recent_logs` - Recent monitoring logs
- `monitor_api.php?action=check_now` - Manual website check

## 📈 Database Tables

### websites
```sql
- id: Website ID
- name: Website name (CheckMe.One)
- url: Website URL (https://checkme.one)
- expected_status: Expected HTTP status (200)
- timeout: Request timeout in seconds
- check_interval: Check interval in seconds
```

### monitoring_logs
```sql
- id: Log entry ID
- website_id: Reference to websites table
- status_code: HTTP response code
- response_time: Response time in milliseconds
- is_online: Boolean (1=online, 0=offline)
- error_message: Error details if any
- check_time: When the check was performed
```

### uptime_stats
```sql
- id: Stats ID
- website_id: Reference to websites table
- date: Date for statistics
- total_checks: Total checks performed
- successful_checks: Successful checks count
- failed_checks: Failed checks count
- avg_response_time: Average response time
- uptime_percentage: Uptime percentage for the day
```

## 🎨 Color Coding

### Uptime Status
- **Green (99%+)**: Excellent uptime
- **Yellow (95-99%)**: Good uptime with some issues
- **Red (<95%)**: Poor uptime, needs attention

### Response Time
- **Green (≤500ms)**: Fast response
- **Yellow (500-1000ms)**: Moderate response
- **Red (>1000ms)**: Slow response

## 🔄 Auto-Refresh

Dashboard automatically refreshes every **30 seconds** to show latest data:
- Current status updates
- New monitoring logs
- Updated statistics
- Refreshed charts

## 🛠️ Manual Testing

Test the monitoring system manually:

1. **Check Current Status**: Click "Check Now" button
2. **View Logs**: Check recent monitoring results
3. **Monitor Response Time**: Watch the response time chart
4. **Test Cron Script**: Run `php monitor_cron.php` manually

## 📱 Mobile Support

Dashboard is fully responsive and works on:
- Desktop browsers
- Tablets
- Mobile phones
- All modern browsers (Chrome, Firefox, Safari, Edge)

## 🔒 Security Features

- **SQL Injection Protection**: PDO prepared statements
- **XSS Protection**: HTML escaping
- **Input Validation**: Proper parameter validation
- **Error Handling**: Graceful error management

## 🚨 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check database credentials in `monitor_config.php`
   - Ensure MySQL server is running
   - Verify database exists

2. **No Data Showing**
   - Import `monitor_database.sql` properly
   - Check if website URL is accessible
   - Verify cron job is running

3. **AJAX Errors**
   - Check browser console for JavaScript errors
   - Ensure all files are uploaded correctly
   - Verify web server configuration

4. **Cron Job Not Working**
   - Check cron job syntax
   - Verify PHP path in cron command
   - Check file permissions

### Performance Tips

- **Database Optimization**: Add indexes for better query performance
- **Log Cleanup**: Regularly clean old monitoring logs
- **Caching**: Implement caching for frequently accessed data
- **CDN**: Use CDN for static assets (Bootstrap, Chart.js)

## 📞 Support

For issues or questions:
1. Check the troubleshooting section
2. Verify all files are properly configured
3. Test individual components separately
4. Check server logs for detailed error messages
