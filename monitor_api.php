<?php
header('Content-Type: application/json');
require_once 'monitor_config.php';

$pdo = getDBConnection();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'current_status':
        getCurrentStatus($pdo);
        break;
    case 'uptime_stats':
        getUptimeStats($pdo);
        break;
    case 'response_time_chart':
        getResponseTimeChart($pdo);
        break;
    case 'recent_logs':
        getRecentLogs($pdo);
        break;
    case 'check_now':
        checkNow($pdo);
        break;
    default:
        echo json_encode(['error' => 'Invalid action']);
}

function getCurrentStatus($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM latest_website_status");
        $websites = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'websites' => $websites,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getUptimeStats($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        
        // Last 24 hours uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 24 HOUR
        ");
        $stmt->execute([$website_id]);
        $stats_24h = $stmt->fetch();
        
        // Last 7 days uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 7 DAY
        ");
        $stmt->execute([$website_id]);
        $stats_7d = $stmt->fetch();
        
        // Last 30 days uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 30 DAY
        ");
        $stmt->execute([$website_id]);
        $stats_30d = $stmt->fetch();
        
        echo json_encode([
            'success' => true,
            'stats_24h' => $stats_24h,
            'stats_7d' => $stats_7d,
            'stats_30d' => $stats_30d
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getResponseTimeChart($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $hours = $_GET['hours'] ?? 24;
        
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(check_time, '%H:%i') as time_label,
                AVG(response_time) as avg_response_time,
                is_online
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL ? HOUR
            GROUP BY DATE_FORMAT(check_time, '%Y-%m-%d %H:%i'), is_online
            ORDER BY check_time ASC
            LIMIT 50
        ");
        $stmt->execute([$website_id, $hours]);
        $chart_data = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'chart_data' => $chart_data
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getRecentLogs($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        
        $stmt = $pdo->prepare("
            SELECT 
                status_code,
                response_time,
                is_online,
                error_message,
                check_time
            FROM monitoring_logs 
            WHERE website_id = ?
            ORDER BY check_time DESC 
            LIMIT ?
        ");
        $stmt->execute([$website_id, $limit]);
        $logs = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'logs' => $logs
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function checkNow($pdo) {
    try {
        global $websites;
        $website = $websites[0]; // checkme.one
        
        $result = checkWebsiteStatus($website['url'], $website['timeout']);
        
        // Log the result
        logMonitoringResult($pdo, $website['id'], $result);
        
        echo json_encode([
            'success' => true,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>
