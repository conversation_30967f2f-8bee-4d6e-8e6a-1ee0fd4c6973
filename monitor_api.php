<?php
// Allow CORS for tracking script
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');
header('Content-Type: application/json');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once 'monitor_config.php';

$pdo = getDBConnection();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'current_status':
        getCurrentStatus($pdo);
        break;
    case 'uptime_stats':
        getUptimeStats($pdo);
        break;
    case 'response_time_chart':
        getResponseTimeChart($pdo);
        break;
    case 'recent_logs':
        getRecentLogs($pdo);
        break;
    case 'check_now':
        checkNow($pdo);
        break;
    case 'user_hits_stats':
        getUserHitsStats($pdo);
        break;
    case 'real_time_hits':
        getRealTimeHits($pdo);
        break;
    case 'hits_chart':
        getHitsChart($pdo);
        break;
    case 'visitor_locations':
        getVisitorLocations($pdo);
        break;
    case 'add_hit':
        addUserHit($pdo);
        break;
    default:
        echo json_encode(['error' => 'Invalid action']);
}

function getCurrentStatus($pdo) {
    try {
        $stmt = $pdo->query("SELECT * FROM latest_website_status");
        $websites = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'websites' => $websites,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getUptimeStats($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        
        // Last 24 hours uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 24 HOUR
        ");
        $stmt->execute([$website_id]);
        $stats_24h = $stmt->fetch();
        
        // Last 7 days uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 7 DAY
        ");
        $stmt->execute([$website_id]);
        $stats_7d = $stmt->fetch();
        
        // Last 30 days uptime
        $stmt = $pdo->prepare("
            SELECT 
                COUNT(*) as total_checks,
                SUM(is_online) as successful_checks,
                AVG(response_time) as avg_response_time,
                (SUM(is_online) / COUNT(*)) * 100 as uptime_percentage
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL 30 DAY
        ");
        $stmt->execute([$website_id]);
        $stats_30d = $stmt->fetch();
        
        echo json_encode([
            'success' => true,
            'stats_24h' => $stats_24h,
            'stats_7d' => $stats_7d,
            'stats_30d' => $stats_30d
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getResponseTimeChart($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $hours = $_GET['hours'] ?? 24;
        
        $stmt = $pdo->prepare("
            SELECT 
                DATE_FORMAT(check_time, '%H:%i') as time_label,
                AVG(response_time) as avg_response_time,
                is_online
            FROM monitoring_logs 
            WHERE website_id = ? AND check_time >= NOW() - INTERVAL ? HOUR
            GROUP BY DATE_FORMAT(check_time, '%Y-%m-%d %H:%i'), is_online
            ORDER BY check_time ASC
            LIMIT 50
        ");
        $stmt->execute([$website_id, $hours]);
        $chart_data = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'chart_data' => $chart_data
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getRecentLogs($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $limit = $_GET['limit'] ?? 20;
        
        $stmt = $pdo->prepare("
            SELECT 
                status_code,
                response_time,
                is_online,
                error_message,
                check_time
            FROM monitoring_logs 
            WHERE website_id = ?
            ORDER BY check_time DESC 
            LIMIT ?
        ");
        $stmt->execute([$website_id, $limit]);
        $logs = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'logs' => $logs
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function checkNow($pdo) {
    try {
        global $websites;
        $website = $websites[0]; // checkme.one

        $result = checkWebsiteStatus($website['url'], $website['timeout']);

        // Log the result
        logMonitoringResult($pdo, $website['id'], $result);

        echo json_encode([
            'success' => true,
            'result' => $result,
            'timestamp' => date('Y-m-d H:i:s')
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getUserHitsStats($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;

        // Real-time stats (last 5 minutes)
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as hits_5min,
                   COUNT(DISTINCT ip_address) as unique_visitors_5min
            FROM user_hits
            WHERE website_id = ? AND hit_time >= NOW() - INTERVAL 5 MINUTE
        ");
        $stmt->execute([$website_id]);
        $realtime = $stmt->fetch();

        // Today's stats
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as hits_today,
                   COUNT(DISTINCT ip_address) as unique_visitors_today,
                   COUNT(DISTINCT session_id) as unique_sessions_today
            FROM user_hits
            WHERE website_id = ? AND DATE(hit_time) = CURDATE()
        ");
        $stmt->execute([$website_id]);
        $today = $stmt->fetch();

        // This hour stats
        $stmt = $pdo->prepare("
            SELECT COUNT(*) as hits_this_hour,
                   COUNT(DISTINCT ip_address) as unique_visitors_this_hour
            FROM user_hits
            WHERE website_id = ? AND hit_time >= DATE_FORMAT(NOW(), '%Y-%m-%d %H:00:00')
        ");
        $stmt->execute([$website_id]);
        $this_hour = $stmt->fetch();

        echo json_encode([
            'success' => true,
            'realtime' => $realtime,
            'today' => $today,
            'this_hour' => $this_hour
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getRealTimeHits($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $limit = $_GET['limit'] ?? 20;

        $stmt = $pdo->prepare("
            SELECT ip_address, user_agent, referer, page_url, country, city, hit_time
            FROM user_hits
            WHERE website_id = ?
            ORDER BY hit_time DESC
            LIMIT ?
        ");
        $stmt->execute([$website_id, $limit]);
        $hits = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'hits' => $hits
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getHitsChart($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;
        $hours = $_GET['hours'] ?? 24;

        $stmt = $pdo->prepare("
            SELECT
                DATE_FORMAT(hit_time, '%H:%i') as time_label,
                COUNT(*) as hits_count,
                COUNT(DISTINCT ip_address) as unique_visitors
            FROM user_hits
            WHERE website_id = ? AND hit_time >= NOW() - INTERVAL ? HOUR
            GROUP BY DATE_FORMAT(hit_time, '%Y-%m-%d %H:%i')
            ORDER BY hit_time ASC
            LIMIT 50
        ");
        $stmt->execute([$website_id, $hours]);
        $chart_data = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'chart_data' => $chart_data
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getVisitorLocations($pdo) {
    try {
        $website_id = $_GET['website_id'] ?? 1;

        $stmt = $pdo->prepare("
            SELECT country, city, COUNT(*) as hits_count,
                   COUNT(DISTINCT ip_address) as unique_visitors
            FROM user_hits
            WHERE website_id = ? AND hit_time >= NOW() - INTERVAL 24 HOUR
            GROUP BY country, city
            ORDER BY hits_count DESC
            LIMIT 10
        ");
        $stmt->execute([$website_id]);
        $locations = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'locations' => $locations
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function addUserHit($pdo) {
    try {
        $website_id = $_POST['website_id'] ?? 1;
        $ip_address = $_POST['ip_address'] ?? $_SERVER['REMOTE_ADDR'];
        $user_agent = $_POST['user_agent'] ?? $_SERVER['HTTP_USER_AGENT'];
        $referer = $_POST['referer'] ?? $_SERVER['HTTP_REFERER'] ?? '';
        $page_url = $_POST['page_url'] ?? '';
        $country = $_POST['country'] ?? 'Unknown';
        $city = $_POST['city'] ?? 'Unknown';
        $session_id = $_POST['session_id'] ?? session_id();

        $stmt = $pdo->prepare("
            INSERT INTO user_hits
            (website_id, ip_address, user_agent, referer, page_url, country, city, session_id)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $website_id,
            $ip_address,
            $user_agent,
            $referer,
            $page_url,
            $country,
            $city,
            $session_id
        ]);

        echo json_encode([
            'success' => true,
            'message' => 'Hit recorded successfully',
            'hit_id' => $pdo->lastInsertId()
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}
