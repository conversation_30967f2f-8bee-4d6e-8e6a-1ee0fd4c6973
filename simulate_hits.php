<?php
/**
 * Simulate user hits for testing the monitoring dashboard
 * Run this script to generate sample user hit data
 */

require_once 'monitor_config.php';

$pdo = getDBConnection();

// Sample data for simulation
$countries = ['Bangladesh', 'India', 'Pakistan', 'USA', 'UK', 'Canada', 'Australia', 'Germany', 'France', 'Japan'];
$cities = [
    'Bangladesh' => ['Dhaka', 'Chittagong', 'Sylhet', 'Rajshahi'],
    'India' => ['Mumbai', 'Delhi', 'Bangalore', 'Kolkata'],
    'Pakistan' => ['Karachi', 'Lahore', 'Islamabad', 'Faisalabad'],
    'USA' => ['New York', 'Los Angeles', 'Chicago', 'Houston'],
    'UK' => ['London', 'Manchester', 'Birmingham', 'Liverpool'],
    'Canada' => ['Toronto', 'Vancouver', 'Montreal', 'Calgary'],
    'Australia' => ['Sydney', 'Melbourne', 'Brisbane', 'Perth'],
    'Germany' => ['Berlin', 'Munich', 'Hamburg', 'Frankfurt'],
    'France' => ['Paris', 'Lyon', 'Marseille', 'Toulouse'],
    'Japan' => ['Tokyo', 'Osaka', 'Kyoto', 'Yokohama']
];

$userAgents = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (Android 11; Mobile; rv:68.0) Gecko/68.0 Firefox/88.0',
    'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
];

$referers = [
    'https://google.com/search?q=checkme.one',
    'https://facebook.com',
    'https://twitter.com',
    'https://linkedin.com',
    'https://bing.com/search?q=website+checker',
    'https://duckduckgo.com',
    'https://yahoo.com',
    ''
];

$pages = [
    'https://checkme.one/',
    'https://checkme.one/about',
    'https://checkme.one/services',
    'https://checkme.one/pricing',
    'https://checkme.one/contact',
    'https://checkme.one/blog',
    'https://checkme.one/help',
    'https://checkme.one/features'
];

function generateRandomIP() {
    return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
}

function generateSessionId() {
    return 'sess_' . uniqid() . '_' . time();
}

// Generate hits for the last 24 hours
$totalHits = 150; // Number of hits to generate
$website_id = 1; // CheckMe.One

echo "Generating {$totalHits} sample user hits...\n";

for ($i = 0; $i < $totalHits; $i++) {
    // Random time within last 24 hours
    $randomMinutes = rand(1, 1440); // 24 hours = 1440 minutes
    $hitTime = date('Y-m-d H:i:s', strtotime("-{$randomMinutes} minutes"));
    
    // Random country and city
    $country = $countries[array_rand($countries)];
    $city = $cities[$country][array_rand($cities[$country])];
    
    // Random other data
    $ip = generateRandomIP();
    $userAgent = $userAgents[array_rand($userAgents)];
    $referer = $referers[array_rand($referers)];
    $pageUrl = $pages[array_rand($pages)];
    $sessionId = generateSessionId();
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO user_hits 
            (website_id, ip_address, user_agent, referer, page_url, country, city, session_id, hit_time) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $website_id,
            $ip,
            $userAgent,
            $referer,
            $pageUrl,
            $country,
            $city,
            $sessionId,
            $hitTime
        ]);
        
        if ($i % 10 == 0) {
            echo "Generated " . ($i + 1) . " hits...\n";
        }
        
    } catch (Exception $e) {
        echo "Error generating hit {$i}: " . $e->getMessage() . "\n";
    }
}

echo "\n✅ Successfully generated {$totalHits} sample user hits!\n";
echo "📊 You can now view the data in your monitoring dashboard.\n";
echo "🔄 Run this script again to generate more sample data.\n";

// Show some statistics
try {
    $stmt = $pdo->prepare("
        SELECT 
            COUNT(*) as total_hits,
            COUNT(DISTINCT ip_address) as unique_ips,
            COUNT(DISTINCT session_id) as unique_sessions,
            COUNT(DISTINCT country) as countries_count
        FROM user_hits 
        WHERE website_id = ? AND hit_time >= NOW() - INTERVAL 24 HOUR
    ");
    $stmt->execute([$website_id]);
    $stats = $stmt->fetch();
    
    echo "\n📈 Current 24h Statistics:\n";
    echo "   Total Hits: {$stats['total_hits']}\n";
    echo "   Unique IPs: {$stats['unique_ips']}\n";
    echo "   Unique Sessions: {$stats['unique_sessions']}\n";
    echo "   Countries: {$stats['countries_count']}\n";
    
} catch (Exception $e) {
    echo "Error getting statistics: " . $e->getMessage() . "\n";
}
?>
