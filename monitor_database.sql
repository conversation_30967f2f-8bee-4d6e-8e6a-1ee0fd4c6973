-- Create database for website monitoring
CREATE DATABASE IF NOT EXISTS website_monitor;
USE website_monitor;

-- Create websites table
CREATE TABLE IF NOT EXISTS websites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    expected_status INT DEFAULT 200,
    timeout INT DEFAULT 30,
    check_interval INT DEFAULT 60,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create monitoring logs table
CREATE TABLE IF NOT EXISTS monitoring_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    status_code INT,
    response_time DECIMAL(10,2),
    is_online BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_check_time (check_time),
    INDEX idx_is_online (is_online),
    FOREI<PERSON><PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Create uptime statistics table
CREATE TABLE IF NOT EXISTS uptime_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    date DATE NOT NULL,
    total_checks INT DEFAULT 0,
    successful_checks INT DEFAULT 0,
    failed_checks INT DEFAULT 0,
    avg_response_time DECIMAL(10,2),
    uptime_percentage DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_website_date (website_id, date),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Insert checkme.one website
INSERT INTO websites (name, url, expected_status, timeout, check_interval) VALUES
('CheckMe.One', 'https://checkme.one', 200, 30, 60);

-- Insert some sample monitoring data for testing
INSERT INTO monitoring_logs (website_id, status_code, response_time, is_online, check_time) VALUES
(1, 200, 245.50, 1, NOW() - INTERVAL 1 MINUTE),
(1, 200, 189.30, 1, NOW() - INTERVAL 2 MINUTE),
(1, 200, 312.75, 1, NOW() - INTERVAL 3 MINUTE),
(1, 200, 156.20, 1, NOW() - INTERVAL 4 MINUTE),
(1, 200, 278.90, 1, NOW() - INTERVAL 5 MINUTE),
(1, 200, 203.45, 1, NOW() - INTERVAL 6 MINUTE),
(1, 200, 167.80, 1, NOW() - INTERVAL 7 MINUTE),
(1, 200, 234.60, 1, NOW() - INTERVAL 8 MINUTE),
(1, 200, 198.30, 1, NOW() - INTERVAL 9 MINUTE),
(1, 200, 289.15, 1, NOW() - INTERVAL 10 MINUTE);

-- Create view for latest status
CREATE VIEW latest_website_status AS
SELECT 
    w.id,
    w.name,
    w.url,
    ml.status_code,
    ml.response_time,
    ml.is_online,
    ml.error_message,
    ml.check_time
FROM websites w
LEFT JOIN monitoring_logs ml ON w.id = ml.website_id
WHERE ml.id = (
    SELECT MAX(id) 
    FROM monitoring_logs ml2 
    WHERE ml2.website_id = w.id
)
AND w.is_active = 1;
