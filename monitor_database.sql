-- Create database for website monitoring
CREATE DATABASE IF NOT EXISTS website_monitor;
USE website_monitor;

-- Create websites table
CREATE TABLE IF NOT EXISTS websites (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    url VARCHAR(500) NOT NULL,
    expected_status INT DEFAULT 200,
    timeout INT DEFAULT 30,
    check_interval INT DEFAULT 60,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create monitoring logs table
CREATE TABLE IF NOT EXISTS monitoring_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    status_code INT,
    response_time DECIMAL(10,2),
    is_online BOOLEAN DEFAULT FALSE,
    error_message TEXT,
    check_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_website_id (website_id),
    INDEX idx_check_time (check_time),
    INDEX idx_is_online (is_online),
    FOREI<PERSON><PERSON> KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Create uptime statistics table
CREATE TABLE IF NOT EXISTS uptime_stats (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    date DATE NOT NULL,
    total_checks INT DEFAULT 0,
    successful_checks INT DEFAULT 0,
    failed_checks INT DEFAULT 0,
    avg_response_time DECIMAL(10,2),
    uptime_percentage DECIMAL(5,2),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_website_date (website_id, date),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Insert checkme.one website
INSERT INTO websites (name, url, expected_status, timeout, check_interval) VALUES
('CheckMe.One', 'https://checkme.one', 200, 30, 60);

-- Insert some sample monitoring data for testing
INSERT INTO monitoring_logs (website_id, status_code, response_time, is_online, check_time) VALUES
(1, 200, 245.50, 1, NOW() - INTERVAL 1 MINUTE),
(1, 200, 189.30, 1, NOW() - INTERVAL 2 MINUTE),
(1, 200, 312.75, 1, NOW() - INTERVAL 3 MINUTE),
(1, 200, 156.20, 1, NOW() - INTERVAL 4 MINUTE),
(1, 200, 278.90, 1, NOW() - INTERVAL 5 MINUTE),
(1, 200, 203.45, 1, NOW() - INTERVAL 6 MINUTE),
(1, 200, 167.80, 1, NOW() - INTERVAL 7 MINUTE),
(1, 200, 234.60, 1, NOW() - INTERVAL 8 MINUTE),
(1, 200, 198.30, 1, NOW() - INTERVAL 9 MINUTE),
(1, 200, 289.15, 1, NOW() - INTERVAL 10 MINUTE);

-- Create user hits tracking table
CREATE TABLE IF NOT EXISTS user_hits (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    referer VARCHAR(500),
    page_url VARCHAR(500),
    country VARCHAR(100),
    city VARCHAR(100),
    hit_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(100),
    INDEX idx_website_id (website_id),
    INDEX idx_hit_time (hit_time),
    INDEX idx_ip_address (ip_address),
    INDEX idx_session_id (session_id),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Create real-time analytics table
CREATE TABLE IF NOT EXISTS analytics_summary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    website_id INT NOT NULL,
    date DATE NOT NULL,
    hour TINYINT NOT NULL,
    total_hits INT DEFAULT 0,
    unique_visitors INT DEFAULT 0,
    unique_ips INT DEFAULT 0,
    page_views INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_website_date_hour (website_id, date, hour),
    FOREIGN KEY (website_id) REFERENCES websites(id) ON DELETE CASCADE
);

-- Insert sample user hits data for testing
INSERT INTO user_hits (website_id, ip_address, user_agent, referer, page_url, country, city, hit_time, session_id) VALUES
(1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://google.com', 'https://checkme.one/', 'Bangladesh', 'Dhaka', NOW() - INTERVAL 1 MINUTE, 'sess_001'),
(1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', 'https://facebook.com', 'https://checkme.one/about', 'Bangladesh', 'Chittagong', NOW() - INTERVAL 2 MINUTE, 'sess_002'),
(1, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'https://twitter.com', 'https://checkme.one/', 'India', 'Mumbai', NOW() - INTERVAL 3 MINUTE, 'sess_003'),
(1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36', 'https://checkme.one/', 'https://checkme.one/services', 'Bangladesh', 'Dhaka', NOW() - INTERVAL 4 MINUTE, 'sess_001'),
(1, '*************', 'Mozilla/5.0 (Android 11; Mobile; rv:68.0)', 'https://google.com', 'https://checkme.one/', 'Pakistan', 'Karachi', NOW() - INTERVAL 5 MINUTE, 'sess_004'),
(1, '*************', 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36', 'https://bing.com', 'https://checkme.one/contact', 'USA', 'New York', NOW() - INTERVAL 6 MINUTE, 'sess_005'),
(1, '*************', 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X)', 'https://checkme.one/about', 'https://checkme.one/pricing', 'Bangladesh', 'Chittagong', NOW() - INTERVAL 7 MINUTE, 'sess_002'),
(1, '*************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:91.0)', 'https://duckduckgo.com', 'https://checkme.one/', 'UK', 'London', NOW() - INTERVAL 8 MINUTE, 'sess_006'),
(1, '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)', 'https://yahoo.com', 'https://checkme.one/', 'Canada', 'Toronto', NOW() - INTERVAL 9 MINUTE, 'sess_007'),
(1, '*************', 'Mozilla/5.0 (iPad; CPU OS 14_7_1 like Mac OS X)', 'https://google.com', 'https://checkme.one/blog', 'Australia', 'Sydney', NOW() - INTERVAL 10 MINUTE, 'sess_008');

-- Create view for latest status
CREATE VIEW latest_website_status AS
SELECT
    w.id,
    w.name,
    w.url,
    ml.status_code,
    ml.response_time,
    ml.is_online,
    ml.error_message,
    ml.check_time
FROM websites w
LEFT JOIN monitoring_logs ml ON w.id = ml.website_id
WHERE ml.id = (
    SELECT MAX(id)
    FROM monitoring_logs ml2
    WHERE ml2.website_id = w.id
)
AND w.is_active = 1;
