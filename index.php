<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - User Login Analytics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .dashboard-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        .dashboard-card:hover {
            transform: translateY(-5px);
        }
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #007bff;
        }
        .online-indicator {
            width: 10px;
            height: 10px;
            background-color: #28a745;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
        .last-updated {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="text-center mb-0">
                    <i class="fas fa-chart-line text-primary"></i>
                    Admin Dashboard
                </h1>
                <p class="text-center text-muted">Real-time User Login Analytics</p>
                <div class="text-center">
                    <small class="last-updated">Last updated: <span id="lastUpdated">Loading...</span></small>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-center">
                    <div class="card-body">
                        <i class="fas fa-users fa-2x text-primary mb-3"></i>
                        <h5 class="card-title">24h Unique Users</h5>
                        <div class="stat-number" id="uniqueUsers24h">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-center">
                    <div class="card-body">
                        <i class="fas fa-wifi fa-2x text-success mb-3"></i>
                        <h5 class="card-title">Currently Online</h5>
                        <div class="stat-number text-success" id="onlineCount">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-center">
                    <div class="card-body">
                        <i class="fas fa-network-wired fa-2x text-warning mb-3"></i>
                        <h5 class="card-title">Shared IPs</h5>
                        <div class="stat-number text-warning" id="sharedIPs">
                            <div class="loading">
                                <i class="fas fa-spinner fa-spin"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card dashboard-card text-center">
                    <div class="card-body">
                        <i class="fas fa-clock fa-2x text-info mb-3"></i>
                        <h5 class="card-title">Auto Refresh</h5>
                        <div class="stat-number text-info" id="refreshCounter">5</div>
                        <small class="text-muted">seconds</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Tables -->
        <div class="row">
            <!-- Online Users -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-circle text-light me-2"></i>
                            Currently Online Users
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Status</th>
                                        <th>User ID</th>
                                        <th>IP Address</th>
                                        <th>Last Activity</th>
                                    </tr>
                                </thead>
                                <tbody id="onlineUsersTable">
                                    <tr>
                                        <td colspan="4" class="loading">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- IP Statistics -->
            <div class="col-md-6 mb-4">
                <div class="card dashboard-card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-share-alt me-2"></i>
                            Multiple Users per IP
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>IP Address</th>
                                        <th>User Count</th>
                                        <th>Users</th>
                                    </tr>
                                </thead>
                                <tbody id="ipStatsTable">
                                    <tr>
                                        <td colspan="3" class="loading">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Logins -->
        <div class="row">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-history me-2"></i>
                            Recent 20 Login Details
                        </h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>User ID</th>
                                        <th>IP Address</th>
                                        <th>Login Time</th>
                                        <th>User Agent</th>
                                    </tr>
                                </thead>
                                <tbody id="recentLoginsTable">
                                    <tr>
                                        <td colspan="4" class="loading">
                                            <i class="fas fa-spinner fa-spin"></i> Loading...
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
