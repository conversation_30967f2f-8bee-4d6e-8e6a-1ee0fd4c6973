// Website Monitor JavaScript
class WebsiteMonitor {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.chart = null;
        this.init();
    }

    init() {
        this.loadAllData();
        this.startAutoRefresh();
        this.initChart();
    }

    async loadAllData() {
        try {
            await Promise.all([
                this.loadCurrentStatus(),
                this.loadUptimeStats(),
                this.loadResponseTimeChart(),
                this.loadRecentLogs()
            ]);
            this.updateLastUpdated();
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    async loadCurrentStatus() {
        try {
            const response = await fetch('monitor_api.php?action=current_status');
            const data = await response.json();
            
            if (data.success && data.websites.length > 0) {
                this.renderCurrentStatus(data.websites[0]);
            } else {
                document.getElementById('currentStatus').innerHTML = 
                    '<div class="alert alert-warning">No website data available</div>';
            }
        } catch (error) {
            document.getElementById('currentStatus').innerHTML = 
                '<div class="alert alert-danger">Error loading status</div>';
        }
    }

    async loadUptimeStats() {
        try {
            const response = await fetch('monitor_api.php?action=uptime_stats&website_id=1');
            const data = await response.json();
            
            if (data.success) {
                this.renderUptimeStats(data);
            }
        } catch (error) {
            console.error('Error loading uptime stats:', error);
        }
    }

    async loadResponseTimeChart() {
        try {
            const response = await fetch('monitor_api.php?action=response_time_chart&website_id=1&hours=24');
            const data = await response.json();
            
            if (data.success) {
                this.updateChart(data.chart_data);
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    }

    async loadRecentLogs() {
        try {
            const response = await fetch('monitor_api.php?action=recent_logs&website_id=1&limit=15');
            const data = await response.json();
            
            if (data.success) {
                this.renderRecentLogs(data.logs);
            }
        } catch (error) {
            console.error('Error loading recent logs:', error);
        }
    }

    renderCurrentStatus(website) {
        const statusIcon = website.is_online ? 
            '<i class="fas fa-check-circle status-online fa-3x"></i>' : 
            '<i class="fas fa-times-circle status-offline fa-3x"></i>';
        
        const statusText = website.is_online ? 'ONLINE' : 'OFFLINE';
        const statusClass = website.is_online ? 'text-success' : 'text-danger';
        
        const responseTimeClass = this.getResponseTimeClass(website.response_time);
        
        document.getElementById('currentStatus').innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    ${statusIcon}
                </div>
                <div class="col-md-10">
                    <h3 class="mb-1">${this.escapeHtml(website.name)}</h3>
                    <p class="mb-2 text-muted">${this.escapeHtml(website.url)}</p>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong> 
                            <span class="${statusClass} fw-bold">${statusText}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>HTTP Code:</strong> 
                            <span class="badge bg-${website.status_code >= 200 && website.status_code < 300 ? 'success' : 'danger'}">
                                ${website.status_code || 'N/A'}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Response Time:</strong> 
                            <span class="${responseTimeClass}">${website.response_time || 'N/A'}ms</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Last Check:</strong> 
                            <small>${this.formatDateTime(website.check_time)}</small>
                        </div>
                    </div>
                    ${website.error_message ? `
                        <div class="alert alert-danger mt-2 mb-0">
                            <strong>Error:</strong> ${this.escapeHtml(website.error_message)}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    renderUptimeStats(data) {
        // 24h stats
        if (data.stats_24h) {
            const uptime24h = parseFloat(data.stats_24h.uptime_percentage || 0).toFixed(2);
            const avgResponse24h = parseFloat(data.stats_24h.avg_response_time || 0).toFixed(0);
            
            document.getElementById('uptime24h').textContent = uptime24h + '%';
            document.getElementById('uptime24h').className = 'metric-value ' + this.getUptimeClass(uptime24h);
            
            document.getElementById('avgResponse24h').textContent = avgResponse24h + 'ms';
            document.getElementById('avgResponse24h').className = 'metric-value ' + this.getResponseTimeClass(avgResponse24h);
            
            document.getElementById('totalChecks24h').textContent = data.stats_24h.total_checks || 0;
        }
        
        // 7d stats
        if (data.stats_7d) {
            const uptime7d = parseFloat(data.stats_7d.uptime_percentage || 0).toFixed(2);
            document.getElementById('uptime7d').textContent = uptime7d + '%';
            document.getElementById('uptime7d').className = 'metric-value ' + this.getUptimeClass(uptime7d);
        }
    }

    renderRecentLogs(logs) {
        const container = document.getElementById('recentLogs');
        
        if (logs.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No recent logs available</p>';
            return;
        }

        container.innerHTML = logs.map(log => {
            const logClass = log.is_online ? 'log-success' : 'log-error';
            const statusIcon = log.is_online ? 
                '<i class="fas fa-check-circle text-success"></i>' : 
                '<i class="fas fa-times-circle text-danger"></i>';
            
            return `
                <div class="log-entry ${logClass}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            ${statusIcon}
                            <strong>${log.status_code || 'N/A'}</strong>
                            <span class="ms-2">${log.response_time || 'N/A'}ms</span>
                        </div>
                        <small>${this.formatTime(log.check_time)}</small>
                    </div>
                    ${log.error_message ? `
                        <div class="mt-1">
                            <small class="text-danger">${this.escapeHtml(log.error_message)}</small>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    initChart() {
        const ctx = document.getElementById('responseTimeChart').getContext('2d');
        this.chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    updateChart(chartData) {
        if (!this.chart || !chartData) return;

        const labels = chartData.map(item => item.time_label);
        const data = chartData.map(item => parseFloat(item.avg_response_time || 0));

        this.chart.data.labels = labels;
        this.chart.data.datasets[0].data = data;
        this.chart.update();
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadAllData();
        }, this.refreshInterval);
    }

    updateLastUpdated() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('lastUpdated').textContent = timeString;
    }

    getUptimeClass(uptime) {
        if (uptime >= 99) return 'uptime-good';
        if (uptime >= 95) return 'uptime-warning';
        return 'uptime-danger';
    }

    getResponseTimeClass(responseTime) {
        if (responseTime <= 500) return 'response-time-good';
        if (responseTime <= 1000) return 'response-time-warning';
        return 'response-time-slow';
    }

    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        const date = new Date(dateTimeString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    formatTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        const date = new Date(dateTimeString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Global function for manual check
async function checkNow() {
    const button = document.querySelector('.btn-check-now');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    button.disabled = true;

    try {
        const response = await fetch('monitor_api.php?action=check_now');
        const data = await response.json();

        if (data.success) {
            // Reload all data to show updated results
            window.monitor.loadAllData();

            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                <strong>Check completed!</strong> Status: ${data.result.is_online ? 'Online' : 'Offline'}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // Auto remove alert after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    } catch (error) {
        console.error('Error during manual check:', error);
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Initialize monitor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.monitor = new WebsiteMonitor();
});
