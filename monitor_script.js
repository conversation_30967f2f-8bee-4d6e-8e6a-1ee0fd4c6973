// Website Monitor JavaScript
class WebsiteMonitor {
    constructor() {
        this.refreshInterval = 30000; // 30 seconds
        this.responseTimeChart = null;
        this.userHitsChart = null;
        this.init();
    }

    init() {
        this.loadAllData();
        this.startAutoRefresh();
        this.initCharts();
    }

    async loadAllData() {
        try {
            await Promise.all([
                this.loadCurrentStatus(),
                this.loadUptimeStats(),
                this.loadUserHitsStats(),
                this.loadResponseTimeChart(),
                this.loadUserHitsChart(),
                this.loadRecentLogs(),
                this.loadRealTimeHits(),
                this.loadVisitorLocations()
            ]);
            this.updateLastUpdated();
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    async loadCurrentStatus() {
        try {
            const response = await fetch('monitor_api.php?action=current_status');
            const data = await response.json();
            
            if (data.success && data.websites.length > 0) {
                this.renderCurrentStatus(data.websites[0]);
            } else {
                document.getElementById('currentStatus').innerHTML = 
                    '<div class="alert alert-warning">No website data available</div>';
            }
        } catch (error) {
            document.getElementById('currentStatus').innerHTML = 
                '<div class="alert alert-danger">Error loading status</div>';
        }
    }

    async loadUptimeStats() {
        try {
            const response = await fetch('monitor_api.php?action=uptime_stats&website_id=1');
            const data = await response.json();
            
            if (data.success) {
                this.renderUptimeStats(data);
            }
        } catch (error) {
            console.error('Error loading uptime stats:', error);
        }
    }

    async loadUserHitsStats() {
        try {
            const response = await fetch('monitor_api.php?action=user_hits_stats&website_id=1');
            const data = await response.json();

            if (data.success) {
                this.renderUserHitsStats(data);
            }
        } catch (error) {
            console.error('Error loading user hits stats:', error);
        }
    }

    async loadResponseTimeChart() {
        try {
            const response = await fetch('monitor_api.php?action=response_time_chart&website_id=1&hours=24');
            const data = await response.json();

            if (data.success) {
                this.updateResponseTimeChart(data.chart_data);
            }
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    }

    async loadUserHitsChart() {
        try {
            const response = await fetch('monitor_api.php?action=hits_chart&website_id=1&hours=24');
            const data = await response.json();

            if (data.success) {
                this.updateUserHitsChart(data.chart_data);
            }
        } catch (error) {
            console.error('Error loading hits chart data:', error);
        }
    }

    async loadRealTimeHits() {
        try {
            const response = await fetch('monitor_api.php?action=real_time_hits&website_id=1&limit=15');
            const data = await response.json();

            if (data.success) {
                this.renderRealTimeHits(data.hits);
            }
        } catch (error) {
            console.error('Error loading real-time hits:', error);
        }
    }

    async loadVisitorLocations() {
        try {
            const response = await fetch('monitor_api.php?action=visitor_locations&website_id=1');
            const data = await response.json();

            if (data.success) {
                this.renderVisitorLocations(data.locations);
            }
        } catch (error) {
            console.error('Error loading visitor locations:', error);
        }
    }

    async loadRecentLogs() {
        try {
            const response = await fetch('monitor_api.php?action=recent_logs&website_id=1&limit=15');
            const data = await response.json();
            
            if (data.success) {
                this.renderRecentLogs(data.logs);
            }
        } catch (error) {
            console.error('Error loading recent logs:', error);
        }
    }

    renderCurrentStatus(website) {
        const statusIcon = website.is_online ? 
            '<i class="fas fa-check-circle status-online fa-3x"></i>' : 
            '<i class="fas fa-times-circle status-offline fa-3x"></i>';
        
        const statusText = website.is_online ? 'ONLINE' : 'OFFLINE';
        const statusClass = website.is_online ? 'text-success' : 'text-danger';
        
        const responseTimeClass = this.getResponseTimeClass(website.response_time);
        
        document.getElementById('currentStatus').innerHTML = `
            <div class="row align-items-center">
                <div class="col-md-2 text-center">
                    ${statusIcon}
                </div>
                <div class="col-md-10">
                    <h3 class="mb-1">${this.escapeHtml(website.name)}</h3>
                    <p class="mb-2 text-muted">${this.escapeHtml(website.url)}</p>
                    <div class="row">
                        <div class="col-md-3">
                            <strong>Status:</strong> 
                            <span class="${statusClass} fw-bold">${statusText}</span>
                        </div>
                        <div class="col-md-3">
                            <strong>HTTP Code:</strong> 
                            <span class="badge bg-${website.status_code >= 200 && website.status_code < 300 ? 'success' : 'danger'}">
                                ${website.status_code || 'N/A'}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <strong>Response Time:</strong> 
                            <span class="${responseTimeClass}">${website.response_time || 'N/A'}ms</span>
                        </div>
                        <div class="col-md-3">
                            <strong>Last Check:</strong> 
                            <small>${this.formatDateTime(website.check_time)}</small>
                        </div>
                    </div>
                    ${website.error_message ? `
                        <div class="alert alert-danger mt-2 mb-0">
                            <strong>Error:</strong> ${this.escapeHtml(website.error_message)}
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    renderUptimeStats(data) {
        // 24h stats
        if (data.stats_24h) {
            const uptime24h = parseFloat(data.stats_24h.uptime_percentage || 0).toFixed(2);
            const avgResponse24h = parseFloat(data.stats_24h.avg_response_time || 0).toFixed(0);

            document.getElementById('uptime24h').textContent = uptime24h + '%';
            document.getElementById('uptime24h').className = 'metric-value ' + this.getUptimeClass(uptime24h);

            document.getElementById('avgResponse24h').textContent = avgResponse24h + 'ms';
            document.getElementById('avgResponse24h').className = 'metric-value ' + this.getResponseTimeClass(avgResponse24h);
        }
    }

    renderUserHitsStats(data) {
        // Real-time stats (5 minutes)
        if (data.realtime) {
            document.getElementById('hitsRealtime').textContent = data.realtime.hits_5min || 0;
        }

        // Today's stats
        if (data.today) {
            document.getElementById('hitsToday').textContent = data.today.hits_today || 0;
            document.getElementById('uniqueVisitorsToday').textContent = data.today.unique_visitors_today || 0;
        }

        // This hour stats
        if (data.this_hour) {
            document.getElementById('hitsThisHour').textContent = data.this_hour.hits_this_hour || 0;
        }
    }

    renderRecentLogs(logs) {
        const container = document.getElementById('recentLogs');

        if (logs.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No recent logs available</p>';
            return;
        }

        container.innerHTML = logs.map(log => {
            const logClass = log.is_online ? 'log-success' : 'log-error';
            const statusIcon = log.is_online ?
                '<i class="fas fa-check-circle text-success"></i>' :
                '<i class="fas fa-times-circle text-danger"></i>';

            return `
                <div class="log-entry ${logClass}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            ${statusIcon}
                            <strong>${log.status_code || 'N/A'}</strong>
                            <span class="ms-2">${log.response_time || 'N/A'}ms</span>
                        </div>
                        <small>${this.formatTime(log.check_time)}</small>
                    </div>
                    ${log.error_message ? `
                        <div class="mt-1">
                            <small class="text-danger">${this.escapeHtml(log.error_message)}</small>
                        </div>
                    ` : ''}
                </div>
            `;
        }).join('');
    }

    renderRealTimeHits(hits) {
        const container = document.getElementById('realTimeHits');

        if (hits.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No recent hits</p>';
            return;
        }

        container.innerHTML = hits.map(hit => {
            const flagIcon = this.getCountryFlag(hit.country);
            const deviceIcon = this.getDeviceIcon(hit.user_agent);

            return `
                <div class="hit-entry mb-2 p-2 border-start border-3 border-primary">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-1">
                                ${flagIcon}
                                <strong class="me-2">${this.escapeHtml(hit.country || 'Unknown')}</strong>
                                <small class="text-muted">${this.escapeHtml(hit.city || 'Unknown')}</small>
                            </div>
                            <div class="d-flex align-items-center">
                                ${deviceIcon}
                                <small class="text-muted me-2">${this.escapeHtml(hit.ip_address)}</small>
                            </div>
                            ${hit.page_url ? `
                                <div class="mt-1">
                                    <small class="text-info">${this.truncateUrl(this.escapeHtml(hit.page_url))}</small>
                                </div>
                            ` : ''}
                        </div>
                        <small class="text-muted">${this.formatTime(hit.hit_time)}</small>
                    </div>
                </div>
            `;
        }).join('');
    }

    renderVisitorLocations(locations) {
        const container = document.getElementById('visitorLocations');

        if (locations.length === 0) {
            container.innerHTML = '<p class="text-muted text-center">No location data available</p>';
            return;
        }

        container.innerHTML = locations.map(location => {
            const flagIcon = this.getCountryFlag(location.country);
            const percentage = ((location.hits_count / locations[0].hits_count) * 100).toFixed(1);

            return `
                <div class="location-entry mb-3 p-2 bg-light rounded">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <div class="d-flex align-items-center">
                            ${flagIcon}
                            <strong>${this.escapeHtml(location.country)}</strong>
                        </div>
                        <span class="badge bg-primary">${location.hits_count}</span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">${this.escapeHtml(location.city)}</small>
                        <small class="text-success">${location.unique_visitors} unique</small>
                    </div>
                    <div class="progress mt-2" style="height: 4px;">
                        <div class="progress-bar" role="progressbar" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    initCharts() {
        this.initResponseTimeChart();
        this.initUserHitsChart();
    }

    initResponseTimeChart() {
        const ctx = document.getElementById('responseTimeChart').getContext('2d');
        this.responseTimeChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Response Time (ms)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    initUserHitsChart() {
        const ctx = document.getElementById('userHitsChart').getContext('2d');
        this.userHitsChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: [],
                datasets: [{
                    label: 'Total Hits',
                    data: [],
                    backgroundColor: 'rgba(40, 167, 69, 0.8)',
                    borderColor: '#28a745',
                    borderWidth: 1
                }, {
                    label: 'Unique Visitors',
                    data: [],
                    backgroundColor: 'rgba(255, 193, 7, 0.8)',
                    borderColor: '#ffc107',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Count'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            }
        });
    }

    updateResponseTimeChart(chartData) {
        if (!this.responseTimeChart || !chartData) return;

        const labels = chartData.map(item => item.time_label);
        const data = chartData.map(item => parseFloat(item.avg_response_time || 0));

        this.responseTimeChart.data.labels = labels;
        this.responseTimeChart.data.datasets[0].data = data;
        this.responseTimeChart.update();
    }

    updateUserHitsChart(chartData) {
        if (!this.userHitsChart || !chartData) return;

        const labels = chartData.map(item => item.time_label);
        const hitsData = chartData.map(item => parseInt(item.hits_count || 0));
        const visitorsData = chartData.map(item => parseInt(item.unique_visitors || 0));

        this.userHitsChart.data.labels = labels;
        this.userHitsChart.data.datasets[0].data = hitsData;
        this.userHitsChart.data.datasets[1].data = visitorsData;
        this.userHitsChart.update();
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadAllData();
        }, this.refreshInterval);
    }

    updateLastUpdated() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('lastUpdated').textContent = timeString;
    }

    getUptimeClass(uptime) {
        if (uptime >= 99) return 'uptime-good';
        if (uptime >= 95) return 'uptime-warning';
        return 'uptime-danger';
    }

    getResponseTimeClass(responseTime) {
        if (responseTime <= 500) return 'response-time-good';
        if (responseTime <= 1000) return 'response-time-warning';
        return 'response-time-slow';
    }

    formatDateTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        const date = new Date(dateTimeString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    formatTime(dateTimeString) {
        if (!dateTimeString) return 'N/A';
        const date = new Date(dateTimeString);
        return date.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: false
        });
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    getCountryFlag(country) {
        const flags = {
            'Bangladesh': '🇧🇩',
            'India': '🇮🇳',
            'Pakistan': '🇵🇰',
            'USA': '🇺🇸',
            'UK': '🇬🇧',
            'Canada': '🇨🇦',
            'Australia': '🇦🇺',
            'Germany': '🇩🇪',
            'France': '🇫🇷',
            'Japan': '🇯🇵'
        };
        return flags[country] || '🌍';
    }

    getDeviceIcon(userAgent) {
        if (!userAgent) return '<i class="fas fa-desktop text-muted"></i>';

        if (userAgent.includes('Mobile') || userAgent.includes('Android')) {
            return '<i class="fas fa-mobile-alt text-primary"></i>';
        } else if (userAgent.includes('iPad') || userAgent.includes('Tablet')) {
            return '<i class="fas fa-tablet-alt text-info"></i>';
        } else {
            return '<i class="fas fa-desktop text-secondary"></i>';
        }
    }

    truncateUrl(url) {
        if (!url) return '';
        return url.length > 40 ? url.substring(0, 40) + '...' : url;
    }
}

// Global function for manual check
async function checkNow() {
    const button = document.querySelector('.btn-check-now');
    const originalText = button.innerHTML;

    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Checking...';
    button.disabled = true;

    try {
        const response = await fetch('monitor_api.php?action=check_now');
        const data = await response.json();

        if (data.success) {
            // Reload all data to show updated results
            window.monitor.loadAllData();

            // Show success message
            const alert = document.createElement('div');
            alert.className = 'alert alert-success alert-dismissible fade show position-fixed';
            alert.style.top = '20px';
            alert.style.right = '20px';
            alert.style.zIndex = '9999';
            alert.innerHTML = `
                <strong>Check completed!</strong> Status: ${data.result.is_online ? 'Online' : 'Offline'}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alert);

            // Auto remove alert after 3 seconds
            setTimeout(() => {
                if (alert.parentNode) {
                    alert.remove();
                }
            }, 3000);
        }
    } catch (error) {
        console.error('Error during manual check:', error);
    } finally {
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

// Initialize monitor when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.monitor = new WebsiteMonitor();
});
