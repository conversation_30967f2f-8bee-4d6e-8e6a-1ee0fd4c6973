# PHP MySQL Admin Dashboard - Real-time User Login Analytics

এই প্রজেক্টটি একটি real-time admin dashboard যা user login analytics দেখায়। এটি AJAX ব্যবহার করে প্রতি 5 সেকেন্ডে automatically update হয়।

## Features

1. **Last 24 hours unique users** - গত ২৪ ঘন্টায় কতজন unique user login করেছে
2. **Same IP different users** - একই IP থেকে কতজন ভিন্ন user login করেছে  
3. **Currently online users** - বর্তমানে online user list (last 5 minute active)
4. **Recent 20 login details** - সর্বশেষ ২০টি login এর বিস্তারিত

## Files Structure

```
├── database.sql       # Database schema এবং sample data
├── config.php         # Database configuration
├── api.php            # AJAX API endpoints
├── index.php          # Main dashboard page
├── script.js          # JavaScript for real-time updates
└── README.md          # This file
```

## Installation

### 1. Database Setup
```sql
-- MySQL/phpMyAdmin এ database.sql file import করুন
-- অথবা manually run করুন:
mysql -u root -p < database.sql
```

### 2. Configuration
`config.php` file এ আপনার database credentials update করুন:
```php
define('DB_HOST', 'localhost');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');
define('DB_NAME', 'admin_dashboard');
```

### 3. Web Server
- XAMPP/WAMP/LAMP server এ files গুলো copy করুন
- অথবা PHP built-in server ব্যবহার করুন:
```bash
php -S localhost:8000
```

### 4. Access Dashboard
Browser এ যান: `http://localhost/` অথবা `http://localhost:8000/`

## Database Table Structure

```sql
CREATE TABLE user_logins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API Endpoints

- `api.php?action=stats` - 24h unique users count
- `api.php?action=online_users` - Currently online users (last 5 min)
- `api.php?action=recent_logins` - Recent 20 login details
- `api.php?action=ip_stats` - Multiple users per IP statistics

## Real-time Features

- **Auto-refresh**: প্রতি 5 সেকেন্ডে data automatically update হয়
- **Live counter**: Refresh countdown timer দেখায়
- **Visual indicators**: Online users এর জন্য animated green dot
- **Responsive design**: Mobile এবং desktop উভয়ে কাজ করে

## Sample Data

Database এ sample data আছে testing এর জন্য। Real production এ আপনার actual login system থেকে data insert করতে হবে।

## Usage Example

আপনার login system এ এই code add করুন:

```php
// User login success এর পর
$pdo = getDBConnection();
$stmt = $pdo->prepare("INSERT INTO user_logins (user_id, ip_address, user_agent) VALUES (?, ?, ?)");
$stmt->execute([
    $user_id,
    $_SERVER['REMOTE_ADDR'],
    $_SERVER['HTTP_USER_AGENT']
]);
```

## Security Notes

- Production এ proper authentication add করুন
- SQL injection protection আছে (PDO prepared statements)
- XSS protection আছে (HTML escaping)
- CSRF protection add করার পরামর্শ দেওয়া হচ্ছে

## Browser Support

- Chrome, Firefox, Safari, Edge (modern versions)
- Mobile browsers supported
- JavaScript enabled required

## Troubleshooting

1. **Database connection error**: config.php এ credentials check করুন
2. **No data showing**: database.sql properly import হয়েছে কিনা check করুন  
3. **AJAX not working**: browser console এ error check করুন
4. **Permission denied**: web server এর file permissions check করুন
