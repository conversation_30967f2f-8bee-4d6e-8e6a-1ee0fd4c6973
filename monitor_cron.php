<?php
/**
 * Automated monitoring script for checkme.one
 * Run this script via cron job every minute:
 * * * * * * /usr/bin/php /path/to/monitor_cron.php
 */

require_once 'monitor_config.php';

// Get database connection
$pdo = getDBConnection();

// Get websites to monitor
global $websites;

foreach ($websites as $website) {
    echo "Checking {$website['name']} ({$website['url']})...\n";
    
    // Check website status
    $result = checkWebsiteStatus($website['url'], $website['timeout']);
    
    // Log the result
    $logged = logMonitoringResult($pdo, $website['id'], $result);
    
    if ($logged) {
        $status = $result['is_online'] ? 'ONLINE' : 'OFFLINE';
        echo "✓ {$website['name']}: {$status} - {$result['response_time']}ms - HTTP {$result['status_code']}\n";
        
        if (!$result['is_online'] && $result['error']) {
            echo "  Error: {$result['error']}\n";
        }
    } else {
        echo "✗ Failed to log result for {$website['name']}\n";
    }
    
    echo "---\n";
}

// Clean up old logs (keep only last 30 days)
try {
    $stmt = $pdo->prepare("DELETE FROM monitoring_logs WHERE check_time < NOW() - INTERVAL 30 DAY");
    $deleted = $stmt->execute();
    
    if ($deleted) {
        $count = $stmt->rowCount();
        if ($count > 0) {
            echo "Cleaned up {$count} old log entries\n";
        }
    }
} catch (Exception $e) {
    echo "Error cleaning up old logs: " . $e->getMessage() . "\n";
}

echo "Monitoring check completed at " . date('Y-m-d H:i:s') . "\n";
?>
