<?php
header('Content-Type: application/json');
require_once 'config.php';

$pdo = getDBConnection();

// Get the action parameter
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'stats':
        getLoginStats($pdo);
        break;
    case 'online_users':
        getOnlineUsers($pdo);
        break;
    case 'recent_logins':
        getRecentLogins($pdo);
        break;
    case 'ip_stats':
        getIPStats($pdo);
        break;
    default:
        echo json_encode(['error' => 'Invalid action']);
}

function getLoginStats($pdo) {
    try {
        // Last 24 hours unique users
        $stmt = $pdo->prepare("
            SELECT COUNT(DISTINCT user_id) as unique_users_24h
            FROM user_logins 
            WHERE login_time >= NOW() - INTERVAL 24 HOUR
        ");
        $stmt->execute();
        $stats = $stmt->fetch();
        
        echo json_encode([
            'success' => true,
            'unique_users_24h' => $stats['unique_users_24h']
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getOnlineUsers($pdo) {
    try {
        // Users active in last 5 minutes
        $stmt = $pdo->prepare("
            SELECT DISTINCT user_id, ip_address, MAX(login_time) as last_activity
            FROM user_logins 
            WHERE login_time >= NOW() - INTERVAL 5 MINUTE
            GROUP BY user_id, ip_address
            ORDER BY last_activity DESC
        ");
        $stmt->execute();
        $users = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'online_users' => $users,
            'count' => count($users)
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getRecentLogins($pdo) {
    try {
        // Recent 20 login details
        $stmt = $pdo->prepare("
            SELECT user_id, ip_address, login_time, user_agent
            FROM user_logins 
            ORDER BY login_time DESC 
            LIMIT 20
        ");
        $stmt->execute();
        $logins = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'recent_logins' => $logins
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}

function getIPStats($pdo) {
    try {
        // Different users from same IP
        $stmt = $pdo->prepare("
            SELECT ip_address, COUNT(DISTINCT user_id) as user_count, 
                   GROUP_CONCAT(DISTINCT user_id) as users
            FROM user_logins 
            GROUP BY ip_address 
            HAVING user_count > 1
            ORDER BY user_count DESC
        ");
        $stmt->execute();
        $ipStats = $stmt->fetchAll();
        
        echo json_encode([
            'success' => true,
            'ip_stats' => $ipStats
        ]);
    } catch (Exception $e) {
        echo json_encode(['error' => $e->getMessage()]);
    }
}
?>
