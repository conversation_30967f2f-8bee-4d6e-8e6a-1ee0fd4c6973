// Dashboard JavaScript for real-time updates
class AdminDashboard {
    constructor() {
        this.refreshInterval = 5000; // 5 seconds
        this.countdownInterval = null;
        this.refreshCounter = 5;
        this.init();
    }

    init() {
        this.loadAllData();
        this.startAutoRefresh();
        this.startCountdown();
    }

    async loadAllData() {
        try {
            await Promise.all([
                this.loadStats(),
                this.loadOnlineUsers(),
                this.loadRecentLogins(),
                this.loadIPStats()
            ]);
            this.updateLastUpdated();
        } catch (error) {
            console.error('Error loading data:', error);
        }
    }

    async loadStats() {
        try {
            const response = await fetch('api.php?action=stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('uniqueUsers24h').textContent = data.unique_users_24h;
            } else {
                document.getElementById('uniqueUsers24h').innerHTML = '<span class="text-danger">Error</span>';
            }
        } catch (error) {
            document.getElementById('uniqueUsers24h').innerHTML = '<span class="text-danger">Error</span>';
        }
    }

    async loadOnlineUsers() {
        try {
            const response = await fetch('api.php?action=online_users');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('onlineCount').textContent = data.count;
                this.renderOnlineUsers(data.online_users);
            } else {
                document.getElementById('onlineCount').innerHTML = '<span class="text-danger">Error</span>';
            }
        } catch (error) {
            document.getElementById('onlineCount').innerHTML = '<span class="text-danger">Error</span>';
        }
    }

    async loadRecentLogins() {
        try {
            const response = await fetch('api.php?action=recent_logins');
            const data = await response.json();
            
            if (data.success) {
                this.renderRecentLogins(data.recent_logins);
            }
        } catch (error) {
            console.error('Error loading recent logins:', error);
        }
    }

    async loadIPStats() {
        try {
            const response = await fetch('api.php?action=ip_stats');
            const data = await response.json();
            
            if (data.success) {
                document.getElementById('sharedIPs').textContent = data.ip_stats.length;
                this.renderIPStats(data.ip_stats);
            } else {
                document.getElementById('sharedIPs').innerHTML = '<span class="text-danger">Error</span>';
            }
        } catch (error) {
            document.getElementById('sharedIPs').innerHTML = '<span class="text-danger">Error</span>';
        }
    }

    renderOnlineUsers(users) {
        const tbody = document.getElementById('onlineUsersTable');
        
        if (users.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No users currently online</td></tr>';
            return;
        }

        tbody.innerHTML = users.map(user => `
            <tr>
                <td><span class="online-indicator"></span>Online</td>
                <td><strong>${this.escapeHtml(user.user_id)}</strong></td>
                <td><code>${this.escapeHtml(user.ip_address)}</code></td>
                <td>${this.formatDateTime(user.last_activity)}</td>
            </tr>
        `).join('');
    }

    renderRecentLogins(logins) {
        const tbody = document.getElementById('recentLoginsTable');
        
        if (logins.length === 0) {
            tbody.innerHTML = '<tr><td colspan="4" class="text-center text-muted">No recent logins</td></tr>';
            return;
        }

        tbody.innerHTML = logins.map(login => `
            <tr>
                <td><strong>${this.escapeHtml(login.user_id)}</strong></td>
                <td><code>${this.escapeHtml(login.ip_address)}</code></td>
                <td>${this.formatDateTime(login.login_time)}</td>
                <td><small>${this.truncateUserAgent(this.escapeHtml(login.user_agent))}</small></td>
            </tr>
        `).join('');
    }

    renderIPStats(ipStats) {
        const tbody = document.getElementById('ipStatsTable');
        
        if (ipStats.length === 0) {
            tbody.innerHTML = '<tr><td colspan="3" class="text-center text-muted">No shared IPs found</td></tr>';
            return;
        }

        tbody.innerHTML = ipStats.map(stat => `
            <tr>
                <td><code>${this.escapeHtml(stat.ip_address)}</code></td>
                <td><span class="badge bg-warning">${stat.user_count}</span></td>
                <td><small>${this.escapeHtml(stat.users)}</small></td>
            </tr>
        `).join('');
    }

    startAutoRefresh() {
        setInterval(() => {
            this.loadAllData();
            this.refreshCounter = 5;
        }, this.refreshInterval);
    }

    startCountdown() {
        this.countdownInterval = setInterval(() => {
            this.refreshCounter--;
            document.getElementById('refreshCounter').textContent = this.refreshCounter;
            
            if (this.refreshCounter <= 0) {
                this.refreshCounter = 5;
            }
        }, 1000);
    }

    updateLastUpdated() {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour12: false,
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('lastUpdated').textContent = timeString;
    }

    formatDateTime(dateTimeString) {
        const date = new Date(dateTimeString);
        return date.toLocaleString('en-US', {
            month: 'short',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });
    }

    truncateUserAgent(userAgent) {
        if (!userAgent) return 'Unknown';
        return userAgent.length > 50 ? userAgent.substring(0, 50) + '...' : userAgent;
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new AdminDashboard();
});

// Add some visual feedback for loading states
document.addEventListener('DOMContentLoaded', function() {
    // Add loading animation to cards
    const cards = document.querySelectorAll('.dashboard-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
});
