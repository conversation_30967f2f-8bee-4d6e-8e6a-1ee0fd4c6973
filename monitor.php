<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Website Monitor - CheckMe.One</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .monitor-card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            background: rgba(255,255,255,0.95);
        }
        .status-online {
            color: #28a745;
            animation: pulse 2s infinite;
        }
        .status-offline {
            color: #dc3545;
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        .metric-card {
            background: linear-gradient(45deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .metric-label {
            color: #6c757d;
            font-size: 0.9rem;
        }
        .uptime-good { color: #28a745; }
        .uptime-warning { color: #ffc107; }
        .uptime-danger { color: #dc3545; }
        .response-time-good { color: #28a745; }
        .response-time-warning { color: #ffc107; }
        .response-time-slow { color: #dc3545; }
        .last-updated {
            font-size: 0.8rem;
            color: #6c757d;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }
        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 5px;
            font-size: 0.9rem;
        }
        .log-success {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
        }
        .log-error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        .btn-check-now {
            background: linear-gradient(45deg, #007bff, #0056b3);
            border: none;
            border-radius: 25px;
            padding: 10px 25px;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        .btn-check-now:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1 class="text-white mb-2">
                    <i class="fas fa-heartbeat"></i>
                    Website Monitor
                </h1>
                <p class="text-white-50">Real-time monitoring for CheckMe.One</p>
                <button class="btn btn-check-now" onclick="checkNow()">
                    <i class="fas fa-sync-alt"></i> Check Now
                </button>
            </div>
        </div>

        <!-- Current Status -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="monitor-card card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-globe"></i>
                            Current Status
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="currentStatus">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                                <p class="mt-2">Loading status...</p>
                            </div>
                        </div>
                        <div class="text-center mt-3">
                            <small class="last-updated">Last updated: <span id="lastUpdated">Loading...</span></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Metrics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value uptime-good" id="uptime24h">--</div>
                    <div class="metric-label">24h Uptime</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value response-time-good" id="avgResponse24h">--</div>
                    <div class="metric-label">Avg Response</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value text-info" id="hitsRealtime">--</div>
                    <div class="metric-label">Live Hits (5min)</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value text-primary" id="hitsToday">--</div>
                    <div class="metric-label">Hits Today</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value text-success" id="uniqueVisitorsToday">--</div>
                    <div class="metric-label">Unique Visitors</div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="metric-card">
                    <div class="metric-value text-warning" id="hitsThisHour">--</div>
                    <div class="metric-label">This Hour</div>
                </div>
            </div>
        </div>

        <!-- Charts and Analytics -->
        <div class="row mb-4">
            <!-- Response Time Chart -->
            <div class="col-md-6 mb-4">
                <div class="monitor-card card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i>
                            Response Time (24h)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="responseTimeChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- User Hits Chart -->
            <div class="col-md-6 mb-4">
                <div class="monitor-card card">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-users"></i>
                            User Hits (24h)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="userHitsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Real-time Data -->
        <div class="row">
            <!-- Real-time User Hits -->
            <div class="col-md-4 mb-4">
                <div class="monitor-card card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-eye"></i>
                            Real-time Hits
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="realTimeHits">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">Loading hits...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Visitor Locations -->
            <div class="col-md-4 mb-4">
                <div class="monitor-card card">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="fas fa-globe-americas"></i>
                            Visitor Locations
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="visitorLocations">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">Loading locations...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Monitoring Logs -->
            <div class="col-md-4 mb-4">
                <div class="monitor-card card">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-list"></i>
                            System Logs
                        </h5>
                    </div>
                    <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                        <div id="recentLogs">
                            <div class="text-center">
                                <i class="fas fa-spinner fa-spin"></i>
                                <p class="mt-2">Loading logs...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="monitor_script.js"></script>
</body>
</html>
